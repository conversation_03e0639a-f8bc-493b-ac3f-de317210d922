from functools import lru_cache
from typing import Union
from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )

    # Application settings
    APP_NAME: str = "Akigo"
    APP_VERSION: str = "0.0.1"
    DEBUG: bool = False
    APP_HOST: str = "http://localhost:8000"
    API_V1_STR: str = "/api/v1"
    ADMIN_API_V1_STR: str = "/admin/v1"

    # Security settings
    SECRET_KEY: str = "your-secret-key"
    REFRESH_SECRET_KEY: str = "your-refresh-secret-key"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_SECONDS: int = 1800  # 30 minutes
    REFRESH_TOKEN_EXPIRE_SECONDS: int = 86400  # 1 day

    # CORS settings
    CORS_ORIGINS: list[str] = ["*"]

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database settings
    MONGODB_URL: str = "mongodb://localhost:27017"
    MONGODB_DB_NAME: str = "akigo_db"

    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"

    # Rate limiting settings
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # in seconds

    # Superuser
    FIRST_SUPERUSER_EMAIL: str = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "adminpassword"

@lru_cache()
def get_settings() -> Settings:
    return Settings()

settings = get_settings()