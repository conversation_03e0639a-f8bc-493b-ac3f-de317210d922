from datetime import datetime, timezone
from typing import Any, Dict, Optional
from uuid import uuid4
from jose import jwt, JWTError, ExpiredSignatureError
from app.core.config import settings
import bcrypt

UTC = timezone.utc

def _now_ts() -> int:
    return int(datetime.now(tz=UTC).timestamp())

def _ts_after(seconds: int) -> int:
    return _now_ts() + seconds

def _jwt_payload(
    data: Dict[str, Any],
    exp_ts: int,
    token_type: str,
    *,
    minimal: bool,
) -> Dict[str, Any]:
    payload: Dict[str, Any] = {
        "exp": exp_ts,
        "iat": _now_ts(),
        "iss": settings.APP_NAME,
        "aud": settings.APP_NAME,
        "jti": str(uuid4()),
        "sub": str(data["id"]),
        "type": token_type,
    }
    if not minimal:
        payload.update({
            "uid": str(data["id"]),
            "role": data.get("role", "user"),
        })
    return payload

def create_password_hash(password: str) -> str:
    """Creates a hashed password using bcrypt.
    Args:
        password (str): The plain text password to hash.
    Returns:
        str: The hashed password.
    """
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifies a plain text password against a hashed password.
    Args:
        plain_password (str): The plain text password to verify.
        hashed_password (str): The hashed password to compare against.
    Returns:
        bool: True if the password matches, False otherwise.
    """
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))

def create_access_token(data: Dict, expires_delta: Optional[int] = None) -> str:
    """
    Generates a JWT token.

    Args:
        data (dict): The payload of the token (user data).
        expires_delta (Optional[int]): Expiration time in seconds. If None, defaults to settings.ACCESS_TOKEN_EXPIRE_SECONDS.
    Returns:
        str: Encoded JWT token as a string.
    """
    if expires_delta is None:
        expires_delta = settings.ACCESS_TOKEN_EXPIRE_SECONDS

    exp_ts = _ts_after(expires_delta)
    claims = _jwt_payload(data, exp_ts, "access", minimal=False)
    return jwt.encode(claims, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

def create_refresh_token(data: Dict, expires_delta: Optional[int] = None) -> str:
    """
    Generates a JWT refresh token.

    Args:
        data (dict): The payload of the token (user data).
        expires_delta (Optional[int]): Expiration time in seconds. If None, defaults to settings.REFRESH_TOKEN_EXPIRE_SECONDS.
    Returns:
        str: Encoded JWT refresh token as a string.
    """
    if expires_delta is None:
        expires_delta = settings.REFRESH_TOKEN_EXPIRE_SECONDS
    exp_ts = _ts_after(expires_delta)
    key = settings.REFRESH_SECRET_KEY or settings.SECRET_KEY
    claims = _jwt_payload(data, exp_ts, "refresh", minimal=True)
    return jwt.encode(claims, key, algorithm=settings.ALGORITHM)

def verify_token(token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
    """
    Verify signature, exp, iss, aud, and token type.
    Returns decoded claims dict if valid, else None.
    """
    try:
        key = settings.SECRET_KEY if token_type == "access" else (settings.REFRESH_SECRET_KEY or settings.SECRET_KEY)
        payload = jwt.decode(
            token,
            key,
            algorithms=[settings.ALGORITHM],
            issuer=settings.APP_NAME,
            audience=settings.APP_NAME,
            options={"require_exp": True, "require_iat": True},
        )
        if payload.get("type") != token_type:
            return None
        payload["sub"] = str(payload.get("sub", ""))
        return payload
    except ExpiredSignatureError:
        return None
    except JWTError:
        return None
    except Exception:
        return None

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """Decode without verifying signature/claims. Use for non-security tasks only."""
    try:
        return jwt.get_unverified_claims(token)
    except Exception:
        return None