from fastapi import APIRouter, HTTPException, status
from fastapi import Depends
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.common.base_response import BaseResponse
from app.core.database import get_db
from app.api.deps import get_user
from app.services.auth.schemas import ChangePasswordRequest, LoginRequest, LoginResponse, RegisterRequest, TokenRefreshRequest, TokenRefreshResponse
from app.services.auth.service import AuthService


router = APIRouter(tags=["Authentication"])

@router.post("/register", response_model=BaseResponse[LoginResponse])
async def register(data: RegisterRequest, db: AsyncIOMotorDatabase = Depends(get_db)):
    try:
        auth_service = AuthService(db)
        return BaseResponse(status=True, message="Registration successful", data=await auth_service.register(data))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.post("/login", response_model=BaseResponse[LoginResponse])
async def login(data: LoginRequest, db: AsyncIOMotorDatabase = Depends(get_db)):
    try:
        auth_service = AuthService(db)
        return BaseResponse(status=True, message="Login successful", data=await auth_service.login(data))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.post("/logout", response_model=BaseResponse[None])
async def logout(user_id: str, db: AsyncIOMotorDatabase = Depends(get_db)):
    try:
        auth_service = AuthService(db)
        await auth_service.logout(user_id)
        return BaseResponse(status=True, message="Logout successful", data=None)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.post("/token/refresh", response_model=BaseResponse[TokenRefreshResponse])
async def refresh_token(data: TokenRefreshRequest, db: AsyncIOMotorDatabase = Depends(get_db)):
    try:
        auth_service = AuthService(db)
        return BaseResponse(status=True, message="Token refreshed successfully", data=await auth_service.refresh_token(data.refresh_token))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.post("/change-password", response_model=BaseResponse[None])
async def change_password(data: ChangePasswordRequest, user: str = Depends(get_user), db: AsyncIOMotorDatabase = Depends(get_db)):
    try:
        auth_service = AuthService(db)
        await auth_service.change_password(data.new_password, user, data.old_password)
        return BaseResponse(status=True, message="Password changed successfully", data=None)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

# == Additional Endpoints For Future Releases ==
@router.post("/reset-password/request")
def reset_password():
    return {"message": "Reset password endpoint"}

@router.post("/reset-password/confirm")
def confirm_reset_password():
    return {"message": "Confirm reset password endpoint"}

@router.post("/verify-email")
def verify_email():
    return {"message": "Verify email endpoint"}


