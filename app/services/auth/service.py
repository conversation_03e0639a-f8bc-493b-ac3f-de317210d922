from app.common.base_service import BaseService
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.security import create_access_token, create_password_hash, create_refresh_token, verify_password
from app.services.auth.schemas import LoginRequest, LoginResponse, RegisterRequest, TokenRefreshResponse, User

class AuthService(BaseService):
    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__(db["users"])

    async def _find_by_username(self, username: str):
        return await self.get({"username": username})

    async def _find_by_email(self, email: str):
        return await self.get({"email": email})

    async def register(self, data: RegisterRequest) -> LoginResponse:
        existing_user = await self._find_by_email(data.email)
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Hash password and prepare user data
        user_data = data.model_dump()
        hash_password = user_data.pop("password")
        user_data["password_hash"] = create_password_hash(hash_password)
        user_data["is_active"] = True
        user_data["role"] = "user"
        user_data["is_registered"] = True
        user_data["is_active"] = True
        user_data = await self.create(user_data)

        # Create tokens
        access_token = create_access_token(user_data)
        refresh_token = create_refresh_token(user_data)

        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            user=User(**user_data),
        )
    
    async def login(self, data: LoginRequest) -> LoginResponse:
        user = await self._find_by_email(data.email)
        if not user:
            raise ValueError("Invalid email or password")

        from app.core.security import verify_password
        if not verify_password(data.password, user["password_hash"]):
            raise ValueError("Invalid email or password")
        
        # Create tokens
        access_token = create_access_token(user)
        refresh_token = create_refresh_token(user)
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            user=User(**user),
        )
        
    async def refresh_token(self, refresh_token: str) -> TokenRefreshResponse:
        from app.core.security import decode_token
        payload = decode_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            raise ValueError("Invalid refresh token")
        
        user = await self._find_by_email(payload["sub"])
        if not user:
            raise ValueError("User not found")

        access_token = create_access_token(user)
        new_refresh_token = create_refresh_token(user)
        return TokenRefreshResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
        )

    async def logout(self, user_id: str):
        # TODO: invalidate token (blacklist, abdm, etc.)
        return {"message": "Logout successful"}
    
    async def change_password(self, new_password: str, user_id: str, old_password: str):
        user = await self.get({"id": user_id})
        if not user:
            raise ValueError("User not found")

        if not verify_password(old_password, user["password_hash"]):
            raise ValueError("Invalid old password")

        hash_password = create_password_hash(new_password)
        await self.update({"_id": user_id}, {"$set": {"password_hash": hash_password}})
        return {"message": "Password changed successfully"}