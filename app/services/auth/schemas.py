from typing import Optional
from pydantic import BaseModel, EmailStr, Field

class TokenRefreshResponse(BaseModel):
    access_token: str = Field(..., description="Access token for authentication",examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."])
    refresh_token: str = Field(..., description="Refresh token to obtain new access tokens",examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."])

class User(BaseModel):
    id: str = Field(..., description="Unique identifier for the user", examples=["507f1f77bcf86cd799439011"])
    email: EmailStr = Field(..., description="User's email address", examples=["<EMAIL>"])
    first_name: str = Field(..., description="User's first name", examples=["<PERSON>"])
    last_name: str = <PERSON>(..., description="User's last name", examples=["Doe"])
    phone_number: Optional[str] = Field(None, description="User's phone number", examples=["+1234567890"])
    is_active: Optional[bool] = Field(True, description="Indicates if the user is active")
    is_registered: Optional[bool] = Field(False, description="Indicates if the user has completed registration")
    last_login: Optional[str] = Field(None, description="Timestamp of the user's last login", examples=["2023-10-01T12:34:56Z"])

class RegisterRequest(BaseModel):
    email: EmailStr = Field(..., description="User's email address",examples=["<EMAIL>"])
    password: str = Field(..., description="User's password", min_length=8, examples=["strongpassword123"])
    username: str = Field(..., description="Desired username", min_length=3, max_length=50, examples=["johndoe"])
    first_name: str = Field(..., description="User's first name", examples=["John"])
    last_name: str = Field(..., description="User's last name", examples=["Doe"])
    phone_number: Optional[str] = Field(None, description="User's phone number", examples=["+1234567890"])
    terms_and_conditions: bool = Field(..., description="Whether the user accepts the terms and conditions")
    privacy_policy: bool = Field(..., description="Whether the user accepts the privacy policy")
    referral_code: Optional[str] = Field(None, description="Referral code if any", examples=["REF12345"])

class LoginRequest(BaseModel):
    email: EmailStr = Field(..., description="User's email address",examples=["<EMAIL>"])
    password: str = Field(..., description="User's password", min_length=8, examples=["strongpassword123"])

class LoginResponse(TokenRefreshResponse):
    user: User

class TokenRefreshRequest(BaseModel):
    refresh_token: str = Field(..., description="Refresh token to obtain new access tokens",examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."])

class ChangePasswordRequest(BaseModel):
    old_password: str = Field(..., description="Current password of the user", min_length=8, examples=["oldpassword123"])
    new_password: str = Field(..., description="New password to set", min_length=8, examples=["newstrongpassword456"])