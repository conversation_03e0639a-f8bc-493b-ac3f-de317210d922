from fastapi import <PERSON><PERSON><PERSON>
from fastapi.concurrency import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from app.core.config import settings
from app.api.v1.routes import router, admin_router
import logging

from app.core.database import close_db, init_db

logger = logging.getLogger(__name__)

# @asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup actions
    logger.info("Starting up the application...")

    try:
        # Initialize database connection
        await init_db()
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise e
    yield
    # Shutdown actions
    logger.info("Shutting down the application...")

    try:
        # Close database connection
        await close_db()
    except Exception as e:
        logger.error(f"Failed to close database: {e}")
        raise e

app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    debug=settings.DEBUG,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan,
)

# TODO: Enable CORS in production with proper origins
# CORS Middleware
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=settings.CORS_ORIGINS,
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )

app.include_router(router, prefix=settings.API_V1_STR)
app.include_router(admin_router, prefix=settings.ADMIN_API_V1_STR)

@app.exception_handler(StarletteHTTPException)
async def http_exc_handler(_, exc: StarletteHTTPException):
    return JSONResponse(
        {"status": False, "message": exc.detail, "data": None},
        status_code=exc.status_code,
    )

@app.exception_handler(RequestValidationError)
async def validation_exc_handler(_, exc: RequestValidationError):
    return JSONResponse(
        {"status": False, "message": "Validation error", "data": exc.errors()},
        status_code=422,
    )

@app.get("/health", tags=["Health"])
async def health():
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app", host="localhost",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )