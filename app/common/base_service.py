# common/base_service.py
from datetime import datetime, UTC

from bson import ObjectId


class BaseService:
    def __init__(self, collection):
        self.collection = collection

    async def get(self, filter: dict):
        if id := filter.pop("id", None):
            filter["_id"] = ObjectId(id)
        doc = await self.collection.find_one(filter)
        if doc:
            doc["id"] = str(doc.pop("_id"))
            return doc
        return None

    async def list(self, filter: dict = {}, limit: int = 100):
        cursor = self.collection.find(filter).limit(limit)
        docs = await cursor.to_list(length=limit)
        for doc in docs:
            doc["id"] = str(doc["_id"])
        return docs

    async def create(self, data: dict):
        data["created_at"] = data.get("created_at") or datetime.now(tz=UTC)
        data["updated_at"] = data.get("updated_at") or datetime.now(tz=UTC)
        result = await self.collection.insert_one(data)
        return await self.get({"_id": result.inserted_id})

    async def update(self, filter: dict, data: dict):
        data["updated_at"] = datetime.now(tz=UTC)
        await self.collection.update_one(filter, {"$set": data})
        return await self.get(filter)

    async def delete(self, filter: dict):
        result = await self.collection.delete_one(filter)
        return result.deleted_count
