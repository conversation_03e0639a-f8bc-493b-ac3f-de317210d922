from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Iterable
from app.core.security import verify_token

security = HTTPBearer(auto_error=True)

def _get_current_user(creds: HTTPAuthorizationCredentials = Depends(security),) -> Dict[str, Any]:
    token = creds.credentials
    claims = verify_token(token, token_type="access")
    if not claims:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return claims

def get_user(user: Dict[str, Any] = Depends(_get_current_user)) -> str:
    return user["uid"]

def require_roles(*allowed: Iterable[str]):
    allowed_set = set(allowed)
    def _checker(current_user: Dict[str, Any] = Depends(_get_current_user)) -> Dict[str, Any]:
        role = current_user.get("role", "user")
        if allowed_set and role not in allowed_set:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Forbidden: insufficient role",
            )
        return current_user
    return _checker